import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EmailSubscriptionRequest {
  enabled: boolean;
  language: 'zh' | 'en';
  topics?: string[];
  platforms?: string[];
  favorites_only?: boolean;
  podcast?: boolean; // Whether to include AI-generated podcast in emails
  timezone?: string;
  send_hour?: number; // 0-23, hour in user's timezone when they want to receive emails
}

/**
 * 邮件订阅管理器
 * 处理用户的邮件订阅偏好设置
 */
Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // 验证用户身份
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ success: false, error: 'Missing authorization header' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 401,
        }
      );
    }

    // Initialize Supabase client with user token
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: authHeader },
        },
      }
    );

    // 获取当前用户
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser();
    if (userError || !user) {
      return new Response(
        JSON.stringify({ success: false, error: 'Unauthorized' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 401,
        }
      );
    }

    if (req.method === 'GET') {
      // 获取用户当前的邮件订阅偏好
      const { data: profile, error: fetchError } = await supabaseClient
        .from('user_profiles')
        .select('preferences')
        .eq('id', user.id)
        .single();

      if (fetchError) {
        console.error('Failed to fetch user profile:', fetchError);
        return new Response(
          JSON.stringify({ success: false, error: 'Failed to fetch preferences' }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500,
          }
        );
      }

      const emailSubscription = profile?.preferences?.email_subscription || {
        enabled: false,
        language: 'zh',
        topics: [],
        platforms: [],
        favorites_only: false,
        podcast: false, // Default to not including podcast
        timezone: 'America/Los_Angeles', // Default to Pacific Time
        send_hour: 8 // Default to 8 AM
      };

      return new Response(
        JSON.stringify({
          success: true,
          subscription: emailSubscription
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      );

    } else if (req.method === 'POST') {
      // 更新用户的邮件订阅偏好
      const requestData: EmailSubscriptionRequest = await req.json();

      // 验证请求数据
      if (typeof requestData.enabled !== 'boolean' ||
          !['zh', 'en'].includes(requestData.language)) {
        return new Response(
          JSON.stringify({ success: false, error: 'Invalid request data' }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 400,
          }
        );
      }

      // 验证时区和发送时间（如果提供）
      if (requestData.timezone && typeof requestData.timezone !== 'string') {
        return new Response(
          JSON.stringify({ success: false, error: 'Invalid timezone format' }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 400,
          }
        );
      }

      if (requestData.send_hour !== undefined &&
          (typeof requestData.send_hour !== 'number' ||
           requestData.send_hour < 0 ||
           requestData.send_hour > 23)) {
        return new Response(
          JSON.stringify({ success: false, error: 'Invalid send_hour: must be between 0-23' }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 400,
          }
        );
      }

      // 获取用户当前偏好
      const { data: currentProfile, error: fetchError } = await supabaseClient
        .from('user_profiles')
        .select('preferences')
        .eq('id', user.id)
        .single();

      if (fetchError) {
        console.error('Failed to fetch current profile:', fetchError);
        return new Response(
          JSON.stringify({ success: false, error: 'Failed to fetch current preferences' }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500,
          }
        );
      }

      // 构建新的偏好设置
      const currentPreferences = currentProfile?.preferences || {};
      const currentEmailSub = currentPreferences.email_subscription || {};
      const updatedPreferences = {
        ...currentPreferences,
        email_subscription: {
          enabled: requestData.enabled,
          language: requestData.language,
          topics: requestData.topics || [],
          platforms: requestData.platforms || [],
          favorites_only: requestData.favorites_only || false,
          podcast: requestData.podcast !== undefined ? requestData.podcast : (currentEmailSub.podcast || false),
          timezone: requestData.timezone || currentEmailSub.timezone || 'America/Los_Angeles',
          send_hour: requestData.send_hour !== undefined ? requestData.send_hour : (currentEmailSub.send_hour || 8),
          subscribed_at: requestData.enabled ? new Date().toISOString() : currentEmailSub.subscribed_at,
          unsubscribed_at: !requestData.enabled ? new Date().toISOString() : undefined
        }
      };

      // 更新数据库
      const { error: updateError } = await supabaseClient
        .from('user_profiles')
        .update({ preferences: updatedPreferences })
        .eq('id', user.id);

      if (updateError) {
        console.error('Failed to update preferences:', updateError);
        return new Response(
          JSON.stringify({ success: false, error: 'Failed to update preferences' }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500,
          }
        );
      }

      // 如果用户启用了邮件订阅，将其添加到相应的Resend audience
      if (requestData.enabled) {
        try {
          await addUserToResendAudience(user, requestData.language);
        } catch (audienceError) {
          console.error('Failed to add user to Resend audience:', audienceError);
          // 不要因为audience操作失败而让整个订阅失败
          // 只记录错误，继续返回成功响应
        }
      } else {
        // 如果用户取消订阅，从Resend audience中移除
        try {
          await removeUserFromResendAudience(user, requestData.language);
        } catch (audienceError) {
          console.error('Failed to remove user from Resend audience:', audienceError);
          // 同样不要因为audience操作失败而让取消订阅失败
        }
      }

      console.log(`Updated email subscription for user ${user.id}: enabled=${requestData.enabled}, language=${requestData.language}`);

      return new Response(
        JSON.stringify({
          success: true,
          message: requestData.enabled ? 'Successfully subscribed to daily emails' : 'Successfully unsubscribed from daily emails',
          subscription: updatedPreferences.email_subscription
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      );

    } else {
      return new Response(
        JSON.stringify({ success: false, error: 'Method not allowed' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 405,
        }
      );
    }

  } catch (error) {
    console.error('Email subscription toggle error:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Internal server error'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});

// 辅助函数：将用户添加到Resend audience
async function addUserToResendAudience(user: any, language: 'zh' | 'en') {
  const resendApiKey = Deno.env.get('RESEND_API_KEY');
  if (!resendApiKey) {
    console.warn('RESEND_API_KEY not found, skipping audience management');
    return;
  }

  // 获取对应语言的audience ID
  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );

  const { data: audienceMapping, error: mappingError } = await supabaseClient
    .from('resend_audiences')
    .select('audience_id')
    .eq('language', language)
    .single();

  if (mappingError || !audienceMapping) {
    console.error(`No audience mapping found for language: ${language}`, mappingError);
    return;
  }

  // 添加用户到audience
  const response = await fetch(`https://api.resend.com/audiences/${audienceMapping.audience_id}/contacts`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${resendApiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      email: user.email,
      first_name: user.user_metadata?.display_name || '',
      unsubscribed: false
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    // 如果用户已存在，不要抛出错误
    if (response.status === 422 && errorText.includes('already exists')) {
      console.log(`User ${user.email} already exists in audience ${audienceMapping.audience_id}`);
      return;
    }
    throw new Error(`Failed to add user to audience: ${response.status} - ${errorText}`);
  }

  console.log(`Added user ${user.email} to ${language} audience: ${audienceMapping.audience_id}`);
}

// 辅助函数：从Resend audience中移除用户
async function removeUserFromResendAudience(user: any, language: 'zh' | 'en') {
  const resendApiKey = Deno.env.get('RESEND_API_KEY');
  if (!resendApiKey) {
    console.warn('RESEND_API_KEY not found, skipping audience management');
    return;
  }

  // 获取对应语言的audience ID
  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );

  const { data: audienceMapping, error: mappingError } = await supabaseClient
    .from('resend_audiences')
    .select('audience_id')
    .eq('language', language)
    .single();

  if (mappingError || !audienceMapping) {
    console.error(`No audience mapping found for language: ${language}`, mappingError);
    return;
  }

  // 首先获取用户在audience中的contact ID
  const listResponse = await fetch(`https://api.resend.com/audiences/${audienceMapping.audience_id}/contacts`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${resendApiKey}`,
      'Content-Type': 'application/json',
    },
  });

  if (!listResponse.ok) {
    console.error(`Failed to list contacts in audience: ${listResponse.status}`);
    return;
  }

  const contactsData = await listResponse.json();
  const userContact = contactsData.data?.find((contact: any) => contact.email === user.email);

  if (!userContact) {
    console.log(`User ${user.email} not found in audience ${audienceMapping.audience_id}`);
    return;
  }

  // 删除用户
  const deleteResponse = await fetch(`https://api.resend.com/audiences/${audienceMapping.audience_id}/contacts/${userContact.id}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${resendApiKey}`,
      'Content-Type': 'application/json',
    },
  });

  if (!deleteResponse.ok) {
    const errorText = await deleteResponse.text();
    throw new Error(`Failed to remove user from audience: ${deleteResponse.status} - ${errorText}`);
  }

  console.log(`Removed user ${user.email} from ${language} audience: ${audienceMapping.audience_id}`);
}
