import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

/**
 * 在指定时区格式化日期
 * @param date 要格式化的日期
 * @param timezone 时区标识符 (如 'America/Los_Angeles')
 * @param locale 语言区域设置 (如 'zh-CN' 或 'en-US')
 * @returns 格式化后的日期字符串
 */
function formatDateInTimezone(date: Date, timezone: string, locale: string): string {
  try {
    return new Intl.DateTimeFormat(locale, {
      timeZone: timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).format(date);
  } catch (error) {
    console.error(`Error formatting date in timezone ${timezone}:`, error);
    // 回退到默认格式
    return new Intl.DateTimeFormat(locale).format(date);
  }
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EmailSubscriptionPreferences {
  enabled: boolean;
  language: 'zh' | 'en';
  topics: string[];
  platforms: string[];
  favorites_only: boolean;
  podcast?: boolean; // Whether to include AI-generated podcast in emails
  subscribed_at?: string;
  unsubscribed_at?: string;
  timezone?: string;
  send_hour?: number; // 0-23, hour in user's timezone when they want to receive emails
}

interface UserProfile {
  id: string;
  email: string;
  preferences?: {
    email_subscription?: EmailSubscriptionPreferences;
  };
}

interface HeadlineData {
  headline: string;
  url: string;
  platform: string;
  source_name: string;
  datasource_language: string;
}

/**
 * Broadcast邮件发送器
 * 使用Resend Broadcast API按语言分组发送邮件
 */
Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('Broadcast Email Sender: Starting broadcast email sending process...');

    // 解析请求体以检查是否强制执行
    let requestData: any = {};
    try {
      if (req.headers.get('content-type')?.includes('application/json')) {
        requestData = await req.json();
      }
    } catch (error) {
      console.log('No JSON body provided, using defaults');
    }

    const isTest = requestData.test === true;
    const forceExecution = requestData.force === true;
    const targetUsers = requestData.target_users; // 可选：指定特定用户列表

    // 检查是否在正确的时间窗口内（Pacific Time 6-8 AM）
    if (!isTest && !forceExecution) {
      const now = new Date();
      const pacificTime = new Date(now.toLocaleString("en-US", {timeZone: "America/Los_Angeles"}));
      const pacificHour = pacificTime.getHours();
      
      console.log(`Broadcast Email Sender: Current Pacific Time: ${pacificTime.toISOString()}, Hour: ${pacificHour}`);
      
      if (pacificHour < 6 || pacificHour >= 8) {
        console.log('Broadcast Email Sender: Not in the correct time window (6-8 AM Pacific Time), skipping');
        return new Response(
          JSON.stringify({
            success: true,
            message: 'Not in the correct time window (6-8 AM Pacific Time), skipping',
            skipped: true
          }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }
    }

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // 1. 获取启用邮件订阅的用户（如果指定了target_users，则只获取这些用户）
    let usersQuery = supabaseClient
      .from('user_profiles')
      .select('id, email, preferences')
      .not('preferences->email_subscription->enabled', 'is', null)
      .eq('preferences->email_subscription->enabled', true);

    if (targetUsers && Array.isArray(targetUsers) && targetUsers.length > 0) {
      usersQuery = usersQuery.in('id', targetUsers);
      console.log(`Broadcast Email Sender: Targeting specific users: ${targetUsers.join(', ')}`);
    }

    const { data: subscribedUsers, error: usersError } = await usersQuery;

    if (usersError) {
      throw new Error(`Failed to fetch subscribed users: ${usersError.message}`);
    }

    if (!subscribedUsers || subscribedUsers.length === 0) {
      console.log('Broadcast Email Sender: No subscribed users found');
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No subscribed users found',
          emailsSent: 0,
          totalUsers: 0
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log(`Broadcast Email Sender: Found ${subscribedUsers.length} subscribed users`);

    // 2. 获取过去24小时的headlines - 分别获取中文和英文
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();

    // 获取中文headlines（来自中文+英文数据源）
    const { data: zhHeadlines, error: zhHeadlinesError } = await supabaseClient
      .from('summaries')
      .select(`
        headline,
        source_urls,
        language,
        metadata,
        posts(
          url,
          datasources(
            id,
            platform,
            source_name,
            language,
            topic_id,
            topics(id, name)
          )
        )
      `)
      .not('headline', 'is', null)
      .eq('language', 'ZH')  // 中文headline
      .gte('created_at', twentyFourHoursAgo)
      .order('created_at', { ascending: false });

    // 获取英文headlines（只来自英文数据源）
    const { data: enHeadlines, error: enHeadlinesError } = await supabaseClient
      .from('summaries')
      .select(`
        headline,
        source_urls,
        language,
        metadata,
        posts(
          url,
          datasources(
            id,
            platform,
            source_name,
            language,
            topic_id,
            topics(id, name)
          )
        )
      `)
      .not('headline', 'is', null)
      .eq('language', 'EN')  // 英文headline
      .gte('created_at', twentyFourHoursAgo)
      .order('created_at', { ascending: false });

    if (zhHeadlinesError) {
      throw new Error(`Failed to fetch ZH headlines: ${zhHeadlinesError.message}`);
    }

    if (enHeadlinesError) {
      throw new Error(`Failed to fetch EN headlines: ${enHeadlinesError.message}`);
    }

    console.log(`Broadcast Email Sender: Found ${zhHeadlines?.length || 0} ZH headlines and ${enHeadlines?.length || 0} EN headlines`);

    if ((!zhHeadlines || zhHeadlines.length === 0) && (!enHeadlines || enHeadlines.length === 0)) {
      console.log('Broadcast Email Sender: No headlines found for the past 24 hours');
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No headlines found for the past 24 hours',
          emailsSent: 0,
          totalUsers: subscribedUsers.length
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // 3. 按语言分组用户并发送broadcast邮件
    const results = await sendBroadcastEmails(
      subscribedUsers, 
      { zhHeadlines: zhHeadlines || [], enHeadlines: enHeadlines || [] }, 
      supabaseClient
    );

    console.log(`Broadcast Email Sender: Successfully sent ${results.totalSent} broadcast emails`);

    return new Response(
      JSON.stringify({
        success: true,
        message: `Successfully sent ${results.totalSent} broadcast emails`,
        emailsSent: results.totalSent,
        totalUsers: subscribedUsers.length,
        broadcasts: results.broadcasts,
        errors: results.errors.length > 0 ? results.errors : undefined
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Broadcast Email Sender: Error:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});

/**
 * 按语言分组发送broadcast邮件
 */
async function sendBroadcastEmails(
  users: UserProfile[],
  headlinesData: { zhHeadlines: any[], enHeadlines: any[] },
  supabaseClient: any
) {
  const results = {
    totalSent: 0,
    broadcasts: [] as any[],
    errors: [] as string[]
  };

  // 按语言分组用户
  const usersByLanguage = {
    zh: users.filter(u => u.preferences?.email_subscription?.language === 'zh'),
    en: users.filter(u => u.preferences?.email_subscription?.language === 'en')
  };

  console.log(`Broadcast Email Sender: ${usersByLanguage.zh.length} Chinese users, ${usersByLanguage.en.length} English users`);

  // 获取audience映射
  const { data: audiences, error: audienceError } = await supabaseClient
    .from('resend_audiences')
    .select('language, audience_id, audience_name');

  if (audienceError) {
    throw new Error(`Failed to fetch audience mappings: ${audienceError.message}`);
  }

  if (!audiences || audiences.length === 0) {
    throw new Error('No audience mappings found. Please run audience-initializer first.');
  }

  const audienceMap = audiences.reduce((acc: any, aud: any) => {
    acc[aud.language] = aud;
    return acc;
  }, {});

  // 为每种语言发送broadcast
  for (const [language, languageUsers] of Object.entries(usersByLanguage)) {
    if (languageUsers.length === 0) {
      console.log(`Broadcast Email Sender: No users for language ${language}, skipping`);
      continue;
    }

    const audienceInfo = audienceMap[language];
    if (!audienceInfo) {
      results.errors.push(`No audience found for language: ${language}`);
      continue;
    }

    try {
      const broadcastResult = await sendLanguageBroadcast(
        language as 'zh' | 'en',
        languageUsers,
        headlinesData,
        audienceInfo,
        supabaseClient
      );

      if (broadcastResult.success) {
        results.totalSent += languageUsers.length;
        results.broadcasts.push({
          language,
          audienceId: audienceInfo.audience_id,
          userCount: languageUsers.length,
          broadcastId: broadcastResult.broadcastId
        });
      } else {
        results.errors.push(`Failed to send ${language} broadcast: ${broadcastResult.error}`);
      }
    } catch (error) {
      results.errors.push(`Error sending ${language} broadcast: ${error.message}`);
    }
  }

  return results;
}

/**
 * 为特定语言发送broadcast邮件
 */
async function sendLanguageBroadcast(
  language: 'zh' | 'en',
  users: UserProfile[],
  headlinesData: { zhHeadlines: any[], enHeadlines: any[] },
  audienceInfo: any,
  supabaseClient: any
) {
  console.log(`Broadcast Email Sender: Sending ${language} broadcast to ${users.length} users`);

  // 获取该语言的所有数据源
  const { data: allDatasources, error: datasourcesError } = await supabaseClient
    .from('datasources')
    .select('id, platform, source_name, language, topic_id, topics(id, name)')
    .eq('active', true);

  if (datasourcesError) {
    throw new Error(`Failed to fetch datasources: ${datasourcesError.message}`);
  }

  // 根据语言选择headlines
  let headlines: any[] = [];
  if (language === 'zh') {
    // 中文用户：获取所有数据源的中文摘要
    if (allDatasources && allDatasources.length > 0) {
      const allDatasourceIds = allDatasources.map(d => d.id);
      const allDatasourceNames = allDatasources.map(d => d.source_name);

      headlines = headlinesData.zhHeadlines.filter(h => {
        const datasourceId = h.metadata?.datasource_id;
        const sourceName = h.metadata?.source_name;
        const idMatch = datasourceId && allDatasourceIds.includes(datasourceId);
        const nameMatch = sourceName && allDatasourceNames.includes(sourceName);
        return idMatch || nameMatch;
      });
    }
  } else {
    // 英文用户：只获取英文数据源的英文摘要
    const enDatasources = allDatasources?.filter(d => d.language === 'EN') || [];
    if (enDatasources.length > 0) {
      const enDatasourceIds = enDatasources.map(d => d.id);
      const enDatasourceNames = enDatasources.map(d => d.source_name);

      headlines = headlinesData.enHeadlines.filter(h => {
        const datasourceId = h.metadata?.datasource_id;
        const sourceName = h.metadata?.source_name;
        const idMatch = datasourceId && enDatasourceIds.includes(datasourceId);
        const nameMatch = sourceName && enDatasourceNames.includes(sourceName);
        return idMatch || nameMatch;
      });
    }
  }

  if (headlines.length === 0) {
    console.log(`Broadcast Email Sender: No headlines for ${language} users, skipping broadcast`);
    return { success: false, error: 'No headlines available' };
  }

  // 生成邮件内容（使用第一个用户的时区作为默认值）
  const defaultTimezone = users[0]?.preferences?.email_subscription?.timezone || 'America/Los_Angeles';
  const emailHtml = generateEmailHtml(headlines, language, 'broadcast', defaultTimezone);

  // 生成邮件主题
  const currentDate = new Date();
  const subject = language === 'zh'
    ? `📧 FeedMe.Today 每日摘要 - ${formatDateInTimezone(currentDate, defaultTimezone, 'zh-CN')}`
    : `📧 FeedMe.Today Daily Summary - ${formatDateInTimezone(currentDate, defaultTimezone, 'en-US')}`;

  // 创建并发送broadcast
  const resendApiKey = Deno.env.get('RESEND_API_KEY');
  if (!resendApiKey) {
    throw new Error('RESEND_API_KEY environment variable is required');
  }

  try {
    // 创建broadcast
    const broadcastResponse = await fetch('https://api.resend.com/broadcasts', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${resendApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        audience_id: audienceInfo.audience_id,
        from: '<EMAIL>',
        subject: subject,
        html: emailHtml,
        reply_to: '<EMAIL>'
      }),
    });

    if (!broadcastResponse.ok) {
      const errorText = await broadcastResponse.text();
      throw new Error(`Failed to create broadcast: ${broadcastResponse.status} - ${errorText}`);
    }

    const broadcastResult = await broadcastResponse.json();
    console.log(`Broadcast Email Sender: Created ${language} broadcast with ID: ${broadcastResult.id}`);

    // 记录邮件发送日志
    await logBroadcastEmails(users, subject, headlines.length, broadcastResult.id, supabaseClient);

    return {
      success: true,
      broadcastId: broadcastResult.id
    };

  } catch (error) {
    console.error(`Failed to send ${language} broadcast:`, error);
    return {
      success: false,
      error: error.message
    };
  }
}
