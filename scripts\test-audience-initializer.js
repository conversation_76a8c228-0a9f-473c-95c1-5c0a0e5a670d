// Test audience initializer
const SUPABASE_URL = 'https://zhqgwljlpddlecmhoeqo.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_SERVICE_ROLE_KEY) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable not set');
  console.log('Please set it in your environment or .env file');
  process.exit(1);
}

async function testAudienceInitializer() {
  console.log('🎯 Testing Audience Initializer...\n');

  try {
    console.log('📡 Calling audience-initializer function...');
    
    const response = await fetch(`${SUPABASE_URL}/functions/v1/audience-initializer`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({})
    });

    console.log(`   📡 API status: ${response.status}`);
    
    const data = await response.json();
    console.log('   📄 Response:', JSON.stringify(data, null, 2));

    if (data.success) {
      console.log(`   ✅ Audience initializer working: ${data.message}`);
      
      if (data.audiences) {
        console.log(`   📊 Audiences created/found: ${data.audiences.length}`);
        data.audiences.forEach(audience => {
          console.log(`     - ${audience.name} (${audience.language}): ${audience.status} - ID: ${audience.id}`);
        });
      }

      if (data.migration) {
        console.log(`   👥 Migration results: ${data.migration.migrated} users migrated`);
        if (data.migration.errors && data.migration.errors.length > 0) {
          console.log(`   ⚠️  Migration errors: ${data.migration.errors.length}`);
          data.migration.errors.forEach(error => {
            console.log(`     - ${error}`);
          });
        }
      }
    } else {
      console.log(`   ❌ Audience initializer failed: ${data.error}`);
    }

  } catch (error) {
    console.error('   ❌ Error calling audience-initializer:', error.message);
  }
}

async function testAudienceManager() {
  console.log('\n🎯 Testing Audience Manager...\n');

  try {
    console.log('📡 Listing audiences...');
    
    const response = await fetch(`${SUPABASE_URL}/functions/v1/audience-manager`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`   📡 API status: ${response.status}`);
    
    const data = await response.json();
    console.log('   📄 Response:', JSON.stringify(data, null, 2));

    if (data.success && data.data) {
      console.log(`   ✅ Found ${data.data.data ? data.data.data.length : 0} audiences`);
      if (data.data.data) {
        data.data.data.forEach(audience => {
          console.log(`     - ${audience.name} (ID: ${audience.id})`);
        });
      }
    } else {
      console.log(`   ❌ Failed to list audiences: ${data.error || 'Unknown error'}`);
    }

  } catch (error) {
    console.error('   ❌ Error calling audience-manager:', error.message);
  }
}

async function main() {
  await testAudienceInitializer();
  await testAudienceManager();
}

main().catch(console.error);
