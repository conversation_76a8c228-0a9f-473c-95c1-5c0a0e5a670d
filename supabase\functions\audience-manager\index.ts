import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface CreateAudienceRequest {
  name: string;
  description?: string;
}

interface CreateContactRequest {
  email: string;
  first_name?: string;
  last_name?: string;
  unsubscribed?: boolean;
}

interface UpdateContactRequest {
  email?: string;
  first_name?: string;
  last_name?: string;
  unsubscribed?: boolean;
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/').filter(Boolean);
    const method = req.method;

    // Initialize Supabase client for logging
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const resendApiKey = Deno.env.get('RESEND_API_KEY');
    if (!resendApiKey) {
      throw new Error('RESEND_API_KEY environment variable is required');
    }

    // Route handling
    if (method === 'GET' && pathSegments.length === 0) {
      // GET /audiences - List all audiences
      return await listAudiences(resendApiKey);
    }

    if (method === 'POST' && pathSegments.length === 0) {
      // POST /audiences - Create audience
      const body = await req.json() as CreateAudienceRequest;
      return await createAudience(resendApiKey, body);
    }

    if (method === 'GET' && pathSegments.length === 1) {
      // GET /audiences/:id - Get specific audience
      const audienceId = pathSegments[0];
      return await getAudience(resendApiKey, audienceId);
    }

    if (method === 'DELETE' && pathSegments.length === 1) {
      // DELETE /audiences/:id - Delete audience
      const audienceId = pathSegments[0];
      return await deleteAudience(resendApiKey, audienceId);
    }

    if (method === 'GET' && pathSegments.length === 2 && pathSegments[1] === 'contacts') {
      // GET /audiences/:id/contacts - List contacts in audience
      const audienceId = pathSegments[0];
      return await listContacts(resendApiKey, audienceId);
    }

    if (method === 'POST' && pathSegments.length === 2 && pathSegments[1] === 'contacts') {
      // POST /audiences/:id/contacts - Create contact in audience
      const audienceId = pathSegments[0];
      const body = await req.json() as CreateContactRequest;
      return await createContact(resendApiKey, audienceId, body);
    }

    if (method === 'GET' && pathSegments.length === 3 && pathSegments[1] === 'contacts') {
      // GET /audiences/:id/contacts/:contactId - Get specific contact
      const audienceId = pathSegments[0];
      const contactId = pathSegments[2];
      return await getContact(resendApiKey, audienceId, contactId);
    }

    if (method === 'PATCH' && pathSegments.length === 3 && pathSegments[1] === 'contacts') {
      // PATCH /audiences/:id/contacts/:contactId - Update contact
      const audienceId = pathSegments[0];
      const contactId = pathSegments[2];
      const body = await req.json() as UpdateContactRequest;
      return await updateContact(resendApiKey, audienceId, contactId, body);
    }

    if (method === 'DELETE' && pathSegments.length === 3 && pathSegments[1] === 'contacts') {
      // DELETE /audiences/:id/contacts/:contactId - Delete contact
      const audienceId = pathSegments[0];
      const contactId = pathSegments[2];
      return await deleteContact(resendApiKey, audienceId, contactId);
    }

    return new Response(
      JSON.stringify({ error: 'Route not found' }),
      { 
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Audience Manager Error:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});

// Audience management functions
async function listAudiences(apiKey: string) {
  const response = await fetch('https://api.resend.com/audiences', {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to list audiences: ${response.status} - ${errorText}`);
  }

  const data = await response.json();
  return new Response(
    JSON.stringify({ success: true, data }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

async function createAudience(apiKey: string, body: CreateAudienceRequest) {
  const response = await fetch('https://api.resend.com/audiences', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to create audience: ${response.status} - ${errorText}`);
  }

  const data = await response.json();
  console.log(`Created audience: ${data.name} (${data.id})`);
  
  return new Response(
    JSON.stringify({ success: true, data }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

async function getAudience(apiKey: string, audienceId: string) {
  const response = await fetch(`https://api.resend.com/audiences/${audienceId}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to get audience: ${response.status} - ${errorText}`);
  }

  const data = await response.json();
  return new Response(
    JSON.stringify({ success: true, data }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

async function deleteAudience(apiKey: string, audienceId: string) {
  const response = await fetch(`https://api.resend.com/audiences/${audienceId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to delete audience: ${response.status} - ${errorText}`);
  }

  console.log(`Deleted audience: ${audienceId}`);
  
  return new Response(
    JSON.stringify({ success: true, message: 'Audience deleted successfully' }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

// Contact management functions
async function listContacts(apiKey: string, audienceId: string) {
  const response = await fetch(`https://api.resend.com/audiences/${audienceId}/contacts`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to list contacts: ${response.status} - ${errorText}`);
  }

  const data = await response.json();
  return new Response(
    JSON.stringify({ success: true, data }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

async function createContact(apiKey: string, audienceId: string, body: CreateContactRequest) {
  const response = await fetch(`https://api.resend.com/audiences/${audienceId}/contacts`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to create contact: ${response.status} - ${errorText}`);
  }

  const data = await response.json();
  console.log(`Created contact: ${body.email} in audience ${audienceId}`);
  
  return new Response(
    JSON.stringify({ success: true, data }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

async function getContact(apiKey: string, audienceId: string, contactId: string) {
  const response = await fetch(`https://api.resend.com/audiences/${audienceId}/contacts/${contactId}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to get contact: ${response.status} - ${errorText}`);
  }

  const data = await response.json();
  return new Response(
    JSON.stringify({ success: true, data }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

async function updateContact(apiKey: string, audienceId: string, contactId: string, body: UpdateContactRequest) {
  const response = await fetch(`https://api.resend.com/audiences/${audienceId}/contacts/${contactId}`, {
    method: 'PATCH',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to update contact: ${response.status} - ${errorText}`);
  }

  const data = await response.json();
  console.log(`Updated contact: ${contactId} in audience ${audienceId}`);
  
  return new Response(
    JSON.stringify({ success: true, data }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}

async function deleteContact(apiKey: string, audienceId: string, contactId: string) {
  const response = await fetch(`https://api.resend.com/audiences/${audienceId}/contacts/${contactId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to delete contact: ${response.status} - ${errorText}`);
  }

  console.log(`Deleted contact: ${contactId} from audience ${audienceId}`);
  
  return new Response(
    JSON.stringify({ success: true, message: 'Contact deleted successfully' }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  );
}
