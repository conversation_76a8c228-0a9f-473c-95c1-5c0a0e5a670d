import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface AudienceConfig {
  name: string;
  description: string;
  language: 'zh' | 'en';
}

const AUDIENCE_CONFIGS: AudienceConfig[] = [
  {
    name: 'FeedMe.Today Chinese Users',
    description: 'Chinese language subscribers for daily summaries',
    language: 'zh'
  },
  {
    name: 'FeedMe.Today English Users', 
    description: 'English language subscribers for daily summaries',
    language: 'en'
  }
];

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const resendApiKey = Deno.env.get('RESEND_API_KEY');
    if (!resendApiKey) {
      throw new Error('RESEND_API_KEY environment variable is required');
    }

    console.log('Starting audience initialization...');

    // Get existing audiences
    const existingAudiences = await listExistingAudiences(resendApiKey);
    console.log(`Found ${existingAudiences.length} existing audiences`);

    const results = [];

    for (const config of AUDIENCE_CONFIGS) {
      // Check if audience already exists
      const existingAudience = existingAudiences.find(a => a.name === config.name);
      
      if (existingAudience) {
        console.log(`Audience "${config.name}" already exists with ID: ${existingAudience.id}`);
        results.push({
          name: config.name,
          id: existingAudience.id,
          status: 'exists',
          language: config.language
        });
        
        // Store audience ID in database for future reference
        await storeAudienceMapping(supabaseClient, config.language, existingAudience.id, config.name);
      } else {
        // Create new audience
        console.log(`Creating audience: ${config.name}`);
        const newAudience = await createAudience(resendApiKey, config);
        
        results.push({
          name: config.name,
          id: newAudience.id,
          status: 'created',
          language: config.language
        });

        // Store audience ID in database
        await storeAudienceMapping(supabaseClient, config.language, newAudience.id, config.name);
        
        console.log(`Created audience: ${config.name} with ID: ${newAudience.id}`);
      }
    }

    // Migrate existing subscribers to appropriate audiences
    const migrationResults = await migrateExistingSubscribers(supabaseClient, resendApiKey, results);

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Audience initialization completed',
        audiences: results,
        migration: migrationResults
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Audience Initializer Error:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});

async function listExistingAudiences(apiKey: string) {
  const response = await fetch('https://api.resend.com/audiences', {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to list audiences: ${response.status} - ${errorText}`);
  }

  const data = await response.json();
  return data.data || [];
}

async function createAudience(apiKey: string, config: AudienceConfig) {
  const response = await fetch('https://api.resend.com/audiences', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      name: config.name,
      description: config.description
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to create audience: ${response.status} - ${errorText}`);
  }

  return await response.json();
}

async function storeAudienceMapping(supabaseClient: any, language: string, audienceId: string, audienceName: string) {
  // Create or update audience mapping in database
  const { error } = await supabaseClient
    .from('resend_audiences')
    .upsert({
      language: language,
      audience_id: audienceId,
      audience_name: audienceName,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }, {
      onConflict: 'language'
    });

  if (error) {
    console.error('Failed to store audience mapping:', error);
    throw new Error(`Failed to store audience mapping: ${error.message}`);
  }
}

async function migrateExistingSubscribers(supabaseClient: any, resendApiKey: string, audiences: any[]) {
  console.log('Starting migration of existing subscribers...');
  
  // Get all users with email subscriptions enabled
  const { data: users, error } = await supabaseClient
    .from('user_profiles')
    .select('id, email, preferences')
    .not('preferences->email_subscription->enabled', 'is', null)
    .eq('preferences->email_subscription->enabled', true);

  if (error) {
    console.error('Failed to fetch users:', error);
    return { error: error.message };
  }

  if (!users || users.length === 0) {
    console.log('No existing subscribers found');
    return { migrated: 0, errors: [] };
  }

  console.log(`Found ${users.length} existing subscribers to migrate`);

  const migrationResults = {
    migrated: 0,
    errors: [] as string[]
  };

  for (const user of users) {
    try {
      const emailPrefs = user.preferences?.email_subscription;
      if (!emailPrefs || !user.email) continue;

      const userLanguage = emailPrefs.language || 'zh';
      const audience = audiences.find(a => a.language === userLanguage);
      
      if (!audience) {
        migrationResults.errors.push(`No audience found for language: ${userLanguage}`);
        continue;
      }

      // Add user to appropriate audience
      const response = await fetch(`https://api.resend.com/audiences/${audience.id}/contacts`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${resendApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: user.email,
          first_name: user.preferences?.display_name || '',
          unsubscribed: false
        }),
      });

      if (response.ok) {
        migrationResults.migrated++;
        console.log(`Migrated user ${user.email} to ${audience.name}`);
      } else {
        const errorText = await response.text();
        // Don't treat "already exists" as an error
        if (response.status === 422 && errorText.includes('already exists')) {
          migrationResults.migrated++;
          console.log(`User ${user.email} already exists in ${audience.name}`);
        } else {
          migrationResults.errors.push(`Failed to migrate ${user.email}: ${errorText}`);
        }
      }
    } catch (error) {
      migrationResults.errors.push(`Error migrating ${user.email}: ${error.message}`);
    }
  }

  console.log(`Migration completed: ${migrationResults.migrated} users migrated, ${migrationResults.errors.length} errors`);
  return migrationResults;
}
